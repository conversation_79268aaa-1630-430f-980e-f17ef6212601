# Production Docker Compose Configuration
# For AI Coding Agent - Multi-tenant container-first development environment
# Usage: docker-compose up -d
# For development: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

# =============================================================================
# DOCKER SECRETS CONFIGURATION
# =============================================================================
# Secrets are stored in ./secrets/ directory and mounted as files in containers
# This provides enterprise-grade security for sensitive configuration
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  supabase_url:
    file: ./secrets/supabase_url.txt
  supabase_key:
    file: ./secrets/supabase_key.txt
  supabase_service_key:
    file: ./secrets/supabase_service_key.txt
  code_server_password:
    file: ./secrets/code_server_password.txt

networks:
  ai-coding-agent-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24
          gateway: **********
    labels:
      - "com.docker.compose.network=ai-coding-agent"
      - "com.docker.compose.project=codingagenttwo"
      - "security.isolation=network-isolated"
      - "security.access-level=internal-only"
  traefik-network:
    driver: bridge
    external: false

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  code_server_data:
    driver: local
  ai_orchestrator_data:
    driver: local

services:
  # Docker Socket Proxy - Secure access to Docker daemon
  # Provides limited, controlled access to Docker operations instead of direct socket mounting
  docker-proxy:
    image: tecnativa/docker-socket-proxy:latest
    container_name: docker-proxy
    environment:
      # Enable only necessary Docker operations for AI orchestrator
      CONTAINERS: 1          # Allow container management (create, start, stop, remove)
      IMAGES: 1             # Allow image operations (pull, list, inspect)
      NETWORKS: 1           # Allow network management for user workspaces
      VOLUMES: 1            # Allow volume management for user data
      SERVICES: 0           # Disable Docker Swarm services
      TASKS: 0              # Disable Docker Swarm tasks
      SECRETS: 0            # Disable Docker Swarm secrets
      CONFIGS: 0            # Disable Docker Swarm configs
      DISTRIBUTION: 0       # Disable distribution API
      SESSION: 0            # Disable session API
      POST: 1               # Allow POST operations (required for container creation)
      BUILD: 0              # Disable build operations for security
      COMMIT: 0             # Disable commit operations
      EXEC: 1               # Allow exec operations for container management
      PING: 1               # Allow ping for health checks
      VERSION: 1            # Allow version info
      INFO: 1               # Allow Docker info
      EVENTS: 1             # Allow Docker events monitoring
      SYSTEM: 0             # Disable system operations
      LOG_LEVEL: info       # Set appropriate log level
    volumes:
      # Mount Docker socket in read-only mode for maximum security
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 32M
    security_opt:
      - no-new-privileges:true
    # Remove read_only and tmpfs that cause permission issues
    # read_only: true
    # tmpfs:
    #   - /tmp:rw,noexec,nosuid,size=10m
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:2375/version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "com.docker.compose.project=codingagenttwo"
      - "com.docker.compose.service=docker-proxy"
      - "security.access-level=internal-only"
      - "security.privilege-level=docker-proxy"
      - "security.isolation=container-hardened"
  # Traefik Reverse Proxy - Entry point for all web traffic
  # Production-hardened configuration with disabled dashboard
  traefik:
    image: traefik:v3.5.1
    container_name: traefik
    command:
      - "--api.dashboard=false"           # Disabled for production security
      - "--api.insecure=false"            # Disabled for production security
      - "--providers.docker=false"        # Disabled - no Docker socket access
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--log.level=WARN"                # Reduced log verbosity for production
      - "--accesslog=false"               # Disabled for production (enable if needed for monitoring)
      - "--metrics.prometheus=true"
      - "--global.sendAnonymousUsage=false" # Privacy enhancement
    ports:
      - "127.0.0.1:80:80"    # Bind to localhost only
      - "127.0.0.1:443:443"  # Bind to localhost only
      # Dashboard port removed for production security
    # Docker socket mount removed for production security
    # volumes:
      # Docker socket access removed - using secure proxy pattern instead
    networks:
      - traefik-network
      - ai-coding-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
    labels:
      - "com.docker.compose.project=codingagenttwo"
      - "com.docker.compose.service=traefik"
      - "security.access-level=external-facing"
      - "security.privilege-level=reverse-proxy"
      - "security.isolation=container-hardened"
      # Dashboard labels removed - dashboard disabled for production security
    healthcheck:
      test: ["CMD", "traefik", "healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  postgresql:
    image: pgvector/pgvector:pg15
    container_name: postgresql
    secrets:
      - postgres_password
    environment:
      POSTGRES_DB: ai_coding_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./containers/postgresql/init-scripts:/docker-entrypoint-initdb.d:ro
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-workspace
    user: postgres
    shm_size: 256m
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ai_coding_agent"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "com.docker.compose.project=codingagenttwo"
      - "com.docker.compose.service=postgresql"
      - "security.access-level=internal-only"
      - "security.privilege-level=database"
      - "security.isolation=network-isolated"

  redis:
    image: redis:7-alpine
    container_name: redis
    command: >
      redis-server
        --maxmemory 512mb
        --maxmemory-policy allkeys-lru
        --tcp-keepalive 60
        --timeout 300
        --tcp-backlog 511
        --databases 16
        --save 900 1
        --save 300 10
        --save 60 10000
        --appendonly yes
        --appendfsync everysec
        --auto-aof-rewrite-percentage 100
        --auto-aof-rewrite-min-size 64mb
        --protected-mode yes
        --port 6379
        --bind 0.0.0.0
    volumes:
      - redis_data:/data
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
        reservations:
          cpus: '0.25'
          memory: 256M
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-workspace
    user: redis
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=50m
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.docker.compose.project=codingagenttwo"
      - "com.docker.compose.service=redis"
      - "security.access-level=internal-only"
      - "security.privilege-level=cache"
      - "security.isolation=network-isolated"



  ai-orchestrator:
    build:
      context: ./containers/ai-orchestrator
      dockerfile: Dockerfile
    container_name: ai-orchestrator
    secrets:
      - postgres_password
      - jwt_secret
      - supabase_url
      - supabase_key
      - supabase_service_key
    # Ports exposed only in development override
    volumes:
      - ai_orchestrator_data:/app/data
      # Removed direct Docker socket mount for security - now using docker-proxy
    environment:
      # Database Configuration (using secrets)
      DATABASE_URL: postgresql+asyncpg://postgres:${POSTGRES_PASSWORD}@postgresql:5432/ai_coding_agent
      JWT_SECRET: ${JWT_SECRET}
      DATABASE_HOST: postgresql
      DATABASE_PORT: 5432
      DATABASE_NAME: ai_coding_agent
      DATABASE_USER: postgres
      REDIS_URL: redis://redis:6379/0

      # Docker Configuration - Secure proxy access
      DOCKER_HOST: tcp://docker-proxy:2375

      # External Service URLs
      # Note: Ollama is expected to run on host machine, not as container
      # If running Ollama as container, change to: http://ollama:11434
      OLLAMA_BASE_URL: ${OLLAMA_BASE_URL:-http://host.docker.internal:11434}

      # AI Service API Keys (non-sensitive, can remain as env vars)
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}

      # LLM Provider Configuration
      DEFAULT_LOCAL_PROVIDER: ${DEFAULT_LOCAL_PROVIDER:-ollama}
      DEFAULT_CLOUD_PROVIDER: ${DEFAULT_CLOUD_PROVIDER:-openrouter}
      ENABLE_CLOUD_FALLBACK: ${ENABLE_CLOUD_FALLBACK:-true}

      # Supabase & Auth (using secrets)
      SUPABASE_URL_FILE: /run/secrets/supabase_url
      SUPABASE_KEY_FILE: /run/secrets/supabase_key
      SUPABASE_SERVICE_KEY_FILE: /run/secrets/supabase_service_key
      JWT_SECRET_FILE: /run/secrets/jwt_secret

      PYTHONPATH: /app

      # Security Configuration
      DOCKER_RESOURCE_LIMITS: ${DOCKER_RESOURCE_LIMITS:-true}
      MAX_USER_CONTAINERS: ${MAX_USER_CONTAINERS:-10}
      CONTAINER_CPU_LIMIT: ${CONTAINER_CPU_LIMIT:-1.0}
      CONTAINER_MEMORY_LIMIT: ${CONTAINER_MEMORY_LIMIT:-2G}
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
      docker-proxy:
        condition: service_healthy
    networks:
      - ai-coding-agent-network
      - traefik-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-workspace
    cap_drop:
      - ALL
    user: appuser
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-orchestrator.rule=Host(`api.localhost`)"
      - "traefik.http.routers.ai-orchestrator.entrypoints=web"
      - "traefik.http.services.ai-orchestrator.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik-network"
      - "com.docker.compose.project=codingagenttwo"
      - "com.docker.compose.service=ai-orchestrator"
      - "security.access-level=external-facing"
      - "security.privilege-level=orchestrator"
      - "security.isolation=container-hardened"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5).raise_for_status()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  user-portal:
    build:
      context: ./containers/admin-dashboard
      dockerfile: Dockerfile
    container_name: user-portal
    # Ports exposed only in development override
    environment:
      # Server-side API URL (for internal container communication)
      API_BASE_URL: http://ai-orchestrator:8000
      # Client-side API URL (accessible from browser) - Use Traefik routing
      NEXT_PUBLIC_API_BASE_URL: http://api.localhost
      NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_KEY}
      NODE_ENV: production
      NEXT_TELEMETRY_DISABLED: "1"
      # Health check configuration
      HEALTH_CHECK_TIMEOUT: "3000"
    depends_on:
      ai-orchestrator:
        condition: service_healthy
    networks:
      - ai-coding-agent-network
      - traefik-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    security_opt:
      - no-new-privileges:true
    user: node
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
      - /home/<USER>/.next/cache:rw,noexec,nosuid,size=200m
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.user-portal.rule=Host(`portal.localhost`)"
      - "traefik.http.routers.user-portal.entrypoints=web"
      - "traefik.http.services.user-portal.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik-network"
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  # Template code-server instance (will be dynamically created for users)
  code-server-template:
    build:
      context: ./containers/code-server
      dockerfile: Dockerfile
    # This service is disabled by default - used as template for user instances
    profiles:
      - template-only
    secrets:
      - code_server_password
    volumes:
      - code_server_data:/home/<USER>
      - ./workspace:/home/<USER>/workspace
      - ./containers/code-server/extensions.json:/home/<USER>/extensions.json:ro
      - ./containers/code-server/settings.json:/home/<USER>/settings.json:ro
      - ./containers/code-server/install-extensions.sh:/home/<USER>/install-extensions.sh:ro
    environment:
      PASSWORD_FILE: /run/secrets/code_server_password
      DOCKER_USER: coder
    networks:
      - ai-coding-agent-network
      - traefik-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined  # Required for code-server functionality
    user: coder
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.code-server-template.rule=Host(`template.localhost`)"
      - "traefik.http.routers.code-server-template.entrypoints=web"
      - "traefik.http.services.code-server-template.loadbalancer.server.port=8080"
      - "traefik.docker.network=traefik-network"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/login"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
