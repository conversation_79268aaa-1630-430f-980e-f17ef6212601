# Development Override Configuration
# For AI Coding Agent - Multi-tenant development environment with hot reload
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

name: ai-coding-agent-dev

services:
  # Traefik development overrides - Re-enable dashboard for development
  traefik:
    command:
      - "--api.dashboard=true"            # Enabled for development
      - "--api.insecure=true"             # Allowed in development only
      - "--providers.docker=true"
      - "--providers.docker.endpoint=tcp://docker-proxy:2375"  # Use secure docker-proxy
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=traefik-network"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--log.level=DEBUG"               # Verbose logging for development
      - "--accesslog=true"                # Access logs for development debugging
      - "--metrics.prometheus=true"
      - "--global.sendAnonymousUsage=false"
    ports:
      - "127.0.0.1:80:80"   # Secure localhost binding
      - "127.0.0.1:443:443" # Secure localhost binding
      - "127.0.0.1:8090:8080" # Traefik dashboard
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.dashboard.service=api@internal"

  ai-orchestrator:
    build:
      context: ./containers/ai-orchestrator
      dockerfile: Dockerfile.dev
    ports:
      - "127.0.0.1:8000:8000"  # Secure localhost binding
      - "127.0.0.1:5678:5678"  # Debug port for debugpy
    volumes:
      - ./containers/ai-orchestrator:/app:delegated
      - ./tests:/app/tests:delegated
      - /app/__pycache__  # Prevent host cache conflicts
    environment:
      # Development environment overrides
      PYTHONPATH: /app
      DEBUG: "true"
      WATCH_FILES: "true"
      PYTHONDONTWRITEBYTECODE: "1"
      PYTHONUNBUFFERED: "1"
      DEBUGPY_WAIT_FOR_CLIENT: "0"
      # Hot-reload optimization
      WATCHDOG_POLLING: "true"
      WATCHDOG_POLL_INTERVAL: "1.0"
      # AI Service API Keys (development)
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      # LLM Configuration
      DEFAULT_LOCAL_PROVIDER: ${DEFAULT_LOCAL_PROVIDER:-ollama}
      DEFAULT_CLOUD_PROVIDER: ${DEFAULT_CLOUD_PROVIDER:-openrouter}
      ENABLE_CLOUD_FALLBACK: ${ENABLE_CLOUD_FALLBACK:-true}
    command: >
      python -m debugpy --listen 0.0.0.0:5678 -m uvicorn src.main:app
        --host 0.0.0.0 --port 8000 --reload --reload-dir /app/src --log-level debug
    develop:
      watch:
        - path: ./containers/ai-orchestrator/src
          action: sync
          target: /app/src
          ignore:
            - __pycache__/
            - "*.pyc"
            - "*.pyo"
            - ".pytest_cache/"
            - ".coverage"
        - path: ./containers/ai-orchestrator/requirements.txt
          action: rebuild

  user-portal:
    build:
      context: ./containers/admin-dashboard
      dockerfile: Dockerfile
    ports:
      - "127.0.0.1:3000:3000"  # Secure localhost binding for development
    volumes:
      - ./containers/admin-dashboard:/home/<USER>/app:delegated
      # - user_portal_node_modules:/home/<USER>/app/node_modules  # Named volume for node_modules (temporarily disabled)
      - user_portal_next:/home/<USER>/app/.next               # Named volume for .next cache
    environment:
      # Development environment configuration
      NODE_ENV: development
      NEXT_TELEMETRY_DISABLED: "1"
      # Development API URLs (for development, use direct container communication)
      API_BASE_URL: http://ai-orchestrator:8000
      NEXT_PUBLIC_API_BASE_URL: http://localhost:8000  # Direct access in dev
      # Hot reload configuration
      WATCHPACK_POLLING: "true"
      FAST_REFRESH: "true"
      CHOKIDAR_USEPOLLING: "true"
      CHOKIDAR_INTERVAL: "1000"
      # Development debugging
      DEBUG: "user-portal:*"
      HEALTH_CHECK_TIMEOUT: "5000"
      # Supabase Auth (replace placeholders with your actual values)
      NEXT_PUBLIC_SUPABASE_URL: https://yiqdlgidpiqtqjabiorr.supabase.co
      NEXT_PUBLIC_SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.wTftZt7OtXkGUC3-qki3KOA_acZbD8g2oQWZ8ywjX2M
    command: npm run dev
    working_dir: /home/<USER>/app
    user: node
    labels:
      - "traefik.http.routers.user-portal-dev.rule=Host(`portal.localhost`)"
    develop:
      watch:
        - path: ./containers/admin-dashboard/src
          action: sync
          target: /home/<USER>/app/src
          ignore:
            - .git/
            - node_modules/
            - .next/
            - .nyc_output/
            - coverage/
            - .DS_Store
            - "*.log"
        - path: ./containers/admin-dashboard/public
          action: sync
          target: /home/<USER>/app/public
          ignore:
            - .DS_Store
        - path: ./containers/admin-dashboard/package.json
          action: rebuild
        - path: ./containers/admin-dashboard/package-lock.json
          action: rebuild
        - path: ./containers/admin-dashboard/next.config.js
          action: restart
        - path: ./containers/admin-dashboard/tailwind.config.js
          action: restart
        - path: ./containers/admin-dashboard/tsconfig.json
          action: restart

  code-server:
    build:
      context: ./containers/code-server
      dockerfile: Dockerfile
    ports:
      - "127.0.0.1:${CODE_SERVER_PORT:-8080}:8080"  # Secure localhost binding
    # Remove command override to use secure password from base configuration
    volumes:
      # Settings and data persistence
      - code_server_data:/home/<USER>
      # Mount entire project for complete development experience
      - .:/home/<USER>/workspace:delegated
    environment:
      SHELL: /bin/bash
      CODE_SERVER_CONFIG_DIR: /home/<USER>/.config/code-server
    develop:
      watch:
        # Watch VS Code configuration changes
        - path: ./containers/code-server/extensions.json
          action: sync
          target: /home/<USER>/extensions.json
        - path: ./containers/code-server/settings.json
          action: sync
          target: /home/<USER>/settings.json
        # Note: Project files are automatically available via volume mount
        # No need to watch individual directories as entire project is mounted

  # Add development database with exposed port for debugging
  postgresql:
    ports:
      - "127.0.0.1:5432:5432"  # Secure localhost binding for development tools
    # Note: Using secure POSTGRES_PASSWORD_FILE from base docker-compose.yml
    # All other configuration inherited from base compose file
    command: postgres -c log_statement=all -c log_destination=stderr

  # Add development Redis with exposed port
  redis:
    ports:
      - "127.0.0.1:6379:6379"  # Secure localhost binding for development tools

# Named volumes for development
volumes:
  user_portal_node_modules:
    driver: local
  user_portal_next:
    driver: local


